# 活动邦 (EventBond) H5系统 - 源码手册

## 软件著作权申请材料

**软件名称：** 活动邦 (EventBond) H5系统  
**版本号：** V1.0  
**开发语言：** TypeScript, JavaScript  
**软件环境：** Node.js + React + NestJS  
**文档类型：** 源码手册  
**页数：** 60页（前30页 + 后30页）

---

## 目录

### 前30页 - 核心功能源码
1. 系统配置与入口文件 (第1-3页)
2. 用户管理系统 (第4-12页)
3. 任务管理系统 (第13-21页)
4. 邀请分享系统 (第22-30页)

### 后30页 - 核心业务逻辑
31. 管理员后台系统 (第31-39页)
32. 数据模型与实体类 (第40-48页)
33. API接口控制器 (第49-57页)
34. 工具类与配置文件 (第58-60页)

---

## 第1页 - 系统主配置文件

**文件路径：** waibao-4/config/index.ts  
**功能说明：** Taro框架主配置文件，定义了多端编译配置、开发环境代理、构建优化等核心配置

```typescript
import { defineConfig, type UserConfigExport } from "@tarojs/cli";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import { UnifiedWebpackPluginV5 } from "weapp-tailwindcss/webpack";
import UnoCSS from 'unocss/webpack'

import devConfig from "./dev";
import prodConfig from "./prod";

// Taro框架配置 - 支持多端开发(H5/小程序/APP)
export default defineConfig(async (merge, { command, mode }) => {
  const baseConfig: UserConfigExport = {
    projectName: "miniprogram", // 项目名称
    date: "2023-9-19", // 创建日期
    designWidth: 750, // 设计稿宽度
    deviceRatio: { // 设备像素比配置
      640: 2.34 / 2,
      750: 1,
      375: 2,
      828: 1.81 / 2,
    },
    sourceRoot: "src", // 源码目录
    outputRoot: "dist", // 输出目录
    plugins: [], // 插件配置
    defineConstants: {}, // 全局常量定义
    copy: { // 文件复制配置
      patterns: [],
      options: {},
    },
    framework: "react", // 使用React框架
    compiler: "webpack5", // 使用Webpack5编译器
    cache: {
      enable: false, // Webpack持久化缓存配置
    },
    mini: { // 小程序端配置
      postcss: {
        htmltransform: {
          enable: true, // HTML转换配置
          config: {
            removeCursorStyle: false,
          },
        },
        pxtransform: { // px转换配置
          enable: true,
          config: {},
        },
        url: { // URL处理配置
          enable: true,
          config: {
            limit: 1024, // 转换尺寸上限
          },
        },
        cssModules: { // CSS模块化配置
          enable: false,
          config: {
            namingPattern: "module",
            generateScopedName: "[name]__[local]___[hash:base64:5]",
          },
        },
      },
      webpackChain(chain) { // Webpack链式配置
        chain.resolve.plugin("tsconfig-paths").use(TsconfigPathsPlugin);
        chain.merge({
          plugin: {
            install: {
              plugin: UnifiedWebpackPluginV5, // TailwindCSS插件
              args: [
                {
                  appType: "taro",
                },
              ],
            },
          },
        });
        chain.plugin('unocss').use(UnoCSS()) // UnoCSS原子化CSS
      },
    },
```

---

## 第2页 - H5端配置与代理设置

**文件路径：** waibao-4/config/index.ts (续)  
**功能说明：** H5端特殊配置，包括开发服务器、API代理、静态资源处理等

```typescript
    h5: { // H5端配置
      esnextModules: ['taro-ui'], // ES6+模块配置
      publicPath: "/", // 公共路径
      staticDirectory: "static", // 静态资源目录
      output: { // 输出文件配置
        filename: "js/[name].[hash:8].js", // JS文件命名规则
        chunkFilename: "js/[name].[chunkhash:8].js", // 代码分割文件命名
      },
      miniCssExtractPluginOption: { // CSS提取插件配置
        ignoreOrder: true,
        filename: "css/[name].[hash].css", // CSS文件命名规则
        chunkFilename: "css/[name].[chunkhash].css",
      },
      postcss: { // PostCSS配置
        autoprefixer: { // 自动添加浏览器前缀
          enable: true,
          config: {},
        },
        cssModules: { // CSS模块化
          enable: false,
          config: {
            namingPattern: "module",
            generateScopedName: "[name]__[local]___[hash:base64:5]",
          },
        },
      },
      webpackChain(chain) { // H5端Webpack配置
        chain.resolve.plugin("tsconfig-paths").use(TsconfigPathsPlugin);
        chain.plugin('unocss').use(UnoCSS())
      },
      devServer: { // 开发服务器配置
        port: 3000, // 前端服务端口
        proxy: { // API代理配置
          '/api': {
            target: 'http://localhost:3001', // 后端服务地址
            changeOrigin: true, // 跨域处理
            secure: false, // HTTPS配置
          }
        }
      },
    },
    rn: { // React Native配置
      appName: "taroDemo",
      postcss: {
        cssModules: {
          enable: false,
        },
      },
    },
  };

  // 环境判断 - 根据编译目标设置输出目录
  if (process.env.TARO_ENV === 'h5') {
    baseConfig.outputRoot = 'dist/h5'; // H5输出目录
  } else if (process.env.TARO_ENV === 'weapp') {
    baseConfig.outputRoot = 'dist/weapp'; // 微信小程序输出目录
  }

  // 环境配置合并
  if (process.env.NODE_ENV === "development") {
    // 开发环境配置（不混淆压缩）
    return merge({}, baseConfig, devConfig);
  }
  // 生产环境配置（开启压缩混淆）
  return merge({}, baseConfig, prodConfig);
});
```

---

## 第3页 - 应用入口文件

**文件路径：** waibao-4/src/app.tsx  
**功能说明：** 应用程序主入口，定义全局配置、路由、生命周期等

```typescript
import { Component, PropsWithChildren } from 'react'
import { useLaunch } from '@tarojs/taro'
import './app.scss'

// 应用主组件 - 整个应用的根组件
function App({ children }: PropsWithChildren<any>) {
  
  // 应用启动生命周期
  useLaunch(() => {
    console.log('App launched.') // 应用启动日志
    
    // 初始化应用配置
    initializeApp()
    
    // 检查用户登录状态
    checkUserLoginStatus()
    
    // 设置全局错误处理
    setupGlobalErrorHandler()
  })

  // 初始化应用配置
  const initializeApp = () => {
    // 设置默认语言
    const defaultLanguage = 'zh_CN'
    Taro.setStorageSync('language', defaultLanguage)
    
    // 初始化用户数据
    initUserData()
    
    // 配置网络请求拦截器
    setupRequestInterceptor()
  }

  // 检查用户登录状态
  const checkUserLoginStatus = () => {
    const token = Taro.getStorageSync('token')
    const adminToken = Taro.getStorageSync('adminToken')
    
    if (token) {
      // 验证普通用户token有效性
      validateUserToken(token)
    }
    
    if (adminToken) {
      // 验证管理员token有效性
      validateAdminToken(adminToken)
    }
  }

  // 验证用户token
  const validateUserToken = async (token: string) => {
    try {
      const response = await Taro.request({
        url: `${process.env.TARO_APP_HOST}/api/user/islogin`,
        method: 'GET',
        header: { token }
      })
      
      if (!response.data.success) {
        // token无效，清除本地存储
        Taro.removeStorageSync('token')
        Taro.removeStorageSync('userInfo')
      } else {
        // 更新用户信息
        Taro.setStorageSync('userInfo', response.data.data)
      }
    } catch (error) {
      console.error('Token validation failed:', error)
      Taro.removeStorageSync('token')
    }
  }

  // 验证管理员token
  const validateAdminToken = async (adminToken: string) => {
    try {
      const response = await Taro.request({
        url: `${process.env.TARO_APP_HOST}/api/admin/verify`,
        method: 'GET',
        header: { token: adminToken }
      })
      
      if (!response.data.success) {
        Taro.removeStorageSync('adminToken')
        Taro.removeStorageSync('adminInfo')
      }
    } catch (error) {
      console.error('Admin token validation failed:', error)
      Taro.removeStorageSync('adminToken')
    }
  }

  // 初始化用户数据
  const initUserData = () => {
    const defaultUserData = {
      omn: 0, // 用户积分
      inviteCount: 0, // 邀请人数
      taskCount: 0, // 任务数量
    }
    
    if (!Taro.getStorageSync('userData')) {
      Taro.setStorageSync('userData', defaultUserData)
    }
  }

  // 设置网络请求拦截器
  const setupRequestInterceptor = () => {
    // 请求拦截器
    Taro.addInterceptor(Taro.interceptors.request, (chain) => {
      const requestParams = chain.requestParams
      
      // 添加通用请求头
      requestParams.header = {
        'Content-Type': 'application/json',
        ...requestParams.header
      }
      
      // 添加token认证
      const token = Taro.getStorageSync('token')
      if (token) {
        requestParams.header.token = token
      }
      
      console.log('Request:', requestParams)
      return chain.proceed(requestParams)
    })
    
    // 响应拦截器
    Taro.addInterceptor(Taro.interceptors.response, (chain) => {
      const response = chain.responseObject
      console.log('Response:', response)
      
      // 统一错误处理
      if (response.statusCode !== 200) {
        handleRequestError(response)
      }
      
      return response
    })
  }

  // 处理请求错误
  const handleRequestError = (response: any) => {
    switch (response.statusCode) {
      case 401:
        // 未授权，跳转登录页
        Taro.removeStorageSync('token')
        Taro.redirectTo({ url: '/pages/part1/login/index' })
        break
      case 403:
        // 权限不足
        Taro.showToast({
          title: '权限不足',
          icon: 'none'
        })
        break
      case 500:
        // 服务器错误
        Taro.showToast({
          title: '服务器错误',
          icon: 'none'
        })
        break
      default:
        Taro.showToast({
          title: '网络错误',
          icon: 'none'
        })
    }
  }

  // 设置全局错误处理
  const setupGlobalErrorHandler = () => {
    // 监听小程序错误
    Taro.onError((error) => {
      console.error('Global error:', error)
      // 可以上报错误到监控系统
      reportError(error)
    })
  }

  // 错误上报
  const reportError = (error: any) => {
    // 这里可以集成错误监控服务
    console.log('Error reported:', error)
  }

  return children
}

export default App
```

---

## 第4页 - 用户登录Hook

**文件路径：** waibao-4/src/pages/hook/useLogin.ts  
**功能说明：** 用户登录状态管理Hook，处理登录验证、白名单页面、自动跳转等逻辑

```typescript
import { useEffect } from "react";
import Taro from "@tarojs/taro";

// 用户登录状态管理Hook
export default function useLogin() {
  let token = Taro.getStorageSync("token"); // 获取本地存储的用户token
  
  // 不需要登录验证的页面路径白名单
  const whiteList = [
    '/pages/part1/login/index',        // 登录页面
    '/pages/part1/register/index',     // 注册页面
    '/pages/part1/reset-password/index', // 重置密码页面
    '/pages/agreement/user/index',     // 用户协议页面
    '/pages/agreement/privacy/index'   // 隐私条款页面
  ];

  useEffect(() => {
    // 获取当前页面路径
    const currentPage = Taro.getCurrentInstance().router?.path;
    
    // 如果当前页面在白名单中，不进行登录验证
    if (currentPage && whiteList.includes(currentPage)) {
      return;
    }

    // 验证用户登录状态
    validateUserLogin();
  }, []);

  // 验证用户登录状态
  const validateUserLogin = () => {
    if (!token) {
      // 没有token，跳转到登录页
      redirectToLogin();
      return;
    }

    // 向后端验证token有效性
    Taro.request({
      url: process.env.TARO_APP_HOST + "/api/user/islogin",
      method: "GET",
      header: {
        token, // 在请求头中携带token
      },
    }).then((res) => {
      if (!res.data.success) {
        // token无效，清除本地存储并跳转登录页
        clearUserData();
        redirectToLogin();
      } else {
        // token有效，更新用户信息
        updateUserInfo(res.data.data);
      }
    }).catch((error) => {
      // 请求失败，可能是网络问题或服务器错误
      console.error("Login validation failed:", error);
      handleLoginValidationError(error);
    });
  };

  // 更新用户信息
  const updateUserInfo = (userData: any) => {
    // 存储用户积分信息
    Taro.setStorageSync("omn", userData.omn);
    
    // 存储完整用户信息
    Taro.setStorageSync("userInfo", {
      id: userData.id,
      username: userData.username,
      email: userData.email,
      omn: userData.omn,
      inviteCode: userData.inviteCode,
      totalInviteCount: userData.totalInviteCount,
      dailyInviteCount: userData.dailyInviteCount,
    });

    // 更新最后登录时间
    Taro.setStorageSync("lastLoginTime", new Date().getTime());
  };

  // 清除用户数据
  const clearUserData = () => {
    Taro.removeStorageSync("token");
    Taro.removeStorageSync("userInfo");
    Taro.removeStorageSync("omn");
    Taro.removeStorageSync("lastLoginTime");
  };

  // 跳转到登录页面
  const redirectToLogin = () => {
    Taro.redirectTo({
      url: "/pages/part1/login/index",
    });
  };

  // 处理登录验证错误
  const handleLoginValidationError = (error: any) => {
    // 根据错误类型进行不同处理
    if (error.statusCode === 401) {
      // 未授权，清除数据并跳转登录
      clearUserData();
      redirectToLogin();
    } else if (error.statusCode === 500) {
      // 服务器错误，显示提示但不强制跳转
      Taro.showToast({
        title: "服务器暂时无法访问",
        icon: "none",
        duration: 2000,
      });
    } else {
      // 网络错误，显示提示
      Taro.showToast({
        title: "网络连接失败",
        icon: "none",
        duration: 2000,
      });
    }
  };

  // 检查token是否即将过期
  const checkTokenExpiration = () => {
    const lastLoginTime = Taro.getStorageSync("lastLoginTime");
    if (lastLoginTime) {
      const currentTime = new Date().getTime();
      const timeDiff = currentTime - lastLoginTime;
      const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数

      // 如果超过一天，提醒用户重新登录
      if (timeDiff > oneDay) {
        Taro.showModal({
          title: "登录过期",
          content: "您的登录已过期，请重新登录",
          showCancel: false,
          success: () => {
            clearUserData();
            redirectToLogin();
          }
        });
      }
    }
  };

  // 定期检查token状态
  useEffect(() => {
    if (token) {
      checkTokenExpiration();
      
      // 设置定时器，每30分钟检查一次
      const interval = setInterval(() => {
        validateUserLogin();
      }, 30 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [token]);

  return token; // 返回token供组件使用
}
```

---

## 第5页 - 用户登录页面

**文件路径：** waibao-4/src/pages/part1/login/index.tsx  
**功能说明：** 用户登录界面，包含邮箱密码登录、表单验证、错误处理等功能

```typescript
import { View, Form, Input, Button } from "@tarojs/components";
import { useState } from "react";
import Taro from "@tarojs/taro";
import useGetLanguage from "../../hook/useGetLanguage";

// 用户登录页面组件
export default function Login() {
  const [loading, setLoading] = useState(false); // 登录加载状态
  const language = useGetLanguage("login"); // 多语言支持

  // 处理登录表单提交
  const formSubmit = (e) => {
    const email = e.detail.value?.email?.toString(); // 获取邮箱输入
    const password = e.detail.value?.password; // 获取密码输入

    // 表单验证 - 检查必填字段
    if (!email || !password) {
      return Taro.showToast({
        title: "请填写完整！",
        icon: "none",
        duration: 2000,
      });
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return Taro.showToast({
        title: "请输入正确的邮箱格式",
        icon: "none",
        duration: 2000,
      });
    }

    // 密码长度验证
    if (password.length < 6 || password.length > 20) {
      return Taro.showToast({
        title: language["Password length error"] || "密码长度必须在6-20位之间",
        icon: "none",
        duration: 2000,
      });
    }

    // 执行登录请求
    performLogin(email, password);
  };

  // 执行登录请求
  const performLogin = async (email: string, password: string) => {
    setLoading(true); // 设置加载状态

    try {
      const response = await Taro.request({
        url: `/api/user/login`, // 登录API接口
        method: "POST",
        data: { email, password }, // 发送登录数据
      });

      handleLoginResponse(response);
    } catch (error) {
      handleLoginError(error);
    } finally {
      setLoading(false); // 清除加载状态
    }
  };

  // 处理登录响应
  const handleLoginResponse = (response: any) => {
    if (response.data.success) {
      // 登录成功处理
      Taro.showToast({
        title: "登录成功",
        icon: "success",
        duration: 2000,
      });

      // 保存用户token和信息
      saveUserData(response.data.data);

      // 跳转到角色选择页面
      Taro.redirectTo({
        url: "/pages/part1/select/index",
      });
    } else {
      // 登录失败处理
      handleLoginFailure(response.data);
    }
  };

  // 保存用户数据
  const saveUserData = (userData: any) => {
    // 保存认证token
    Taro.setStorageSync("token", userData.token);
    
    // 保存用户基本信息
    Taro.setStorageSync("userInfo", {
      id: userData.id,
      username: userData.username,
      email: userData.email,
      omn: userData.omn, // 用户积分
      inviteCode: userData.inviteCode, // 邀请码
      approved: userData.approved, // 审核状态
    });

    // 保存登录时间
    Taro.setStorageSync("loginTime", new Date().getTime());
  };

  // 处理登录失败
  const handleLoginFailure = (errorData: any) => {
    let errorMessage = errorData.data || "登录失败";
    
    // 处理不同类型的错误消息
    if (typeof errorMessage === 'object' && errorMessage.message) {
      errorMessage = errorMessage.message;
    }

    // 根据错误类型显示不同提示
    if (errorMessage.includes("邮箱")) {
      errorMessage = "邮箱不存在或格式错误";
    } else if (errorMessage.includes("密码")) {
      errorMessage = "密码错误";
    } else if (errorMessage.includes("审核")) {
      errorMessage = "账户待审核，请联系管理员";
    }

    Taro.showToast({
      title: errorMessage,
      icon: "none",
      duration: 2000,
    });
  };

  // 处理登录错误
  const handleLoginError = (error: any) => {
    console.error("Login error:", error);
    
    let errorMessage = "登录失败";
    
    if (error.data?.data) {
      errorMessage = error.data.data;
      if (typeof errorMessage === 'object' && errorMessage.message) {
        errorMessage = errorMessage.message;
      }
    }

    Taro.showToast({
      title: errorMessage,
      icon: "none",
      duration: 2000,
    });
  };

  // 跳转到注册页面
  const goToRegister = () => {
    Taro.navigateTo({
      url: "/pages/part1/register/index",
    });
  };

  // 跳转到忘记密码页面
  const goToResetPassword = () => {
    Taro.navigateTo({
      url: "/pages/part1/reset-password/index",
    });
  };

  return (
    <View className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <View className="sm:mx-auto sm:w-full sm:max-w-md">
        <View className="text-center">
          <View className="text-3xl font-bold text-gray-900 mb-2">
            {language["Welcome Back"] || "欢迎回来"}
          </View>
          <View className="text-sm text-gray-600">
            {language["Sign in to App"] || "登录到活动邦"}
          </View>
        </View>
      </View>

      <View className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <View className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <Form onSubmit={formSubmit}>
            {/* 邮箱输入框 */}
            <View className="mb-6">
              <View className="text-sm font-medium text-gray-700 mb-2">
                {language["Email"] || "邮箱地址"}
              </View>
              <Input
                name="email"
                type="text"
                placeholder={language["Please enter your email"] || "请输入邮箱地址"}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </View>

            {/* 密码输入框 */}
            <View className="mb-6">
              <View className="text-sm font-medium text-gray-700 mb-2">
                {language["Password"] || "密码"}
              </View>
              <Input
                name="password"
                type="password"
                placeholder={language["Please enter your password"] || "请输入密码"}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </View>

            {/* 忘记密码链接 */}
            <View className="flex justify-end mb-6">
              <View 
                className="text-sm text-blue-600 cursor-pointer hover:text-blue-500"
                onClick={goToResetPassword}
              >
                {language["forget the password"] || "忘记密码？"}
              </View>
            </View>

            {/* 登录按钮 */}
            <Button
              formType="submit"
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              loading={loading}
            >
              {loading ? "登录中..." : (language["login"] || "登录")}
            </Button>
          </Form>

          {/* 注册链接 */}
          <View className="mt-6 text-center">
            <View className="text-sm text-gray-600">
              还没有账户？
              <View 
                className="text-blue-600 cursor-pointer hover:text-blue-500 ml-1"
                onClick={goToRegister}
              >
                {language["register"] || "立即注册"}
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}
```

